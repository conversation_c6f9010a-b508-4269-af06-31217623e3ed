<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
        }

        .logo {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 107, 53, 0.3);
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
            margin: 0 40px;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px 12px 50px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.15);
            border-color: #ff6b35;
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-btn {
            position: relative;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-btn:hover {
            background: rgba(255, 107, 53, 0.2);
            transform: scale(1.1);
        }

        .profile-img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .profile-img:hover {
            transform: scale(1.1);
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr 320px;
            gap: 30px;
            padding: 30px 0;
            min-height: calc(100vh - 80px);
        }

        /* Sidebar */
        .sidebar {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 110px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar h3 {
            color: #ff6b35;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: 600;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            margin-bottom: 8px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: linear-gradient(45deg, rgba(255, 107, 53, 0.2), rgba(247, 147, 30, 0.2));
            border-color: #ff6b35;
        }

        .nav-icon {
            font-size: 20px;
            color: #ff6b35;
        }

        /* Feed Section */
        .feed {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .feed-header {
            padding: 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feed-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .story-prompt {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 107, 53, 0.3);
            margin-bottom: 20px;
        }

        .story-prompt h4 {
            color: #ff6b35;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .story-prompt p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .create-post-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .create-post-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        }

        .create-post-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .create-post-btn:hover::before {
            left: 100%;
        }

        .feed-content {
            padding: 30px;
        }

        .load-more {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .load-more:hover {
            color: #ff6b35;
        }

        /* Trending Sidebar */
        .trending {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 110px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .trending h3 {
            color: #ff6b35;
            margin-bottom: 25px;
            font-size: 20px;
            font-weight: 600;
        }

        .trending-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .trending-item:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateY(-2px);
        }

        .trending-hashtag {
            color: #ff6b35;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .trending-count {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* Content Sections */
        .content-section {
            transition: opacity 0.3s ease-in-out;
            opacity: 1;
        }

        .content-section:not(.active) {
            display: none !important;
            opacity: 0;
        }

        .content-section.active {
            display: block !important;
            opacity: 1;
        }

        /* Section Content Styles */
        .explore-content, .messages-content, .profile-content, .communities-content, .analytics-content {
            padding: 20px;
            text-align: center;
        }

        .explore-content h3, .messages-content h3, .profile-content h3, .communities-content h3, .analytics-content h3 {
            color: #ff6b35;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .explore-content p, .messages-content p, .communities-content p, .analytics-content p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .explore-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .category-tag {
            background: rgba(255, 107, 53, 0.2);
            color: #ff6b35;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 107, 53, 0.3);
        }

        .category-tag:hover {
            background: rgba(255, 107, 53, 0.3);
            transform: translateY(-2px);
        }

        /* Profile Styles */
        .profile-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
        }

        .profile-info h3 {
            color: #ffffff;
            margin-bottom: 5px;
            font-size: 24px;
        }

        .profile-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
        }

        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 30px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 28px;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        }

        /* Communities Styles */
        .communities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .community-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .community-card:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateY(-5px);
        }

        .community-card h4 {
            color: #ff6b35;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .community-card p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-size: 14px;
        }

        .join-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .join-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }

        /* Analytics Styles */
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .analytics-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .analytics-card:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateY(-5px);
        }

        .analytics-card h4 {
            color: #ff6b35;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .analytics-number {
            font-size: 36px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .analytics-card p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            margin: 0;
        }

        /* Messages Styles */
        .messages-placeholder {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 20px;
        }

        .messages-placeholder p {
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .sidebar, .feed, .trending {
            animation: fadeIn 0.6s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 280px 1fr 300px;
                gap: 20px;
            }
        }

        @media (max-width: 960px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .sidebar, .trending {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }

            .search-container {
                max-width: 100%;
                margin: 0;
            }

            .container {
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">Naroop</div>
                <div class="search-container">
                    <div class="search-icon">🔍</div>
                    <input type="text" class="search-input" placeholder="Search communities, topics, people...">
                </div>
                <div class="user-actions">
                    <button class="notification-btn">🔔</button>
                    <div class="profile-img">N</div>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content">
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="nav-item active" data-section="feed">
                    <div class="nav-icon">📱</div>
                    <span>Feed</span>
                </div>
                <div class="nav-item" data-section="explore">
                    <div class="nav-icon">🌟</div>
                    <span>Explore</span>
                </div>
                <div class="nav-item" data-section="messages">
                    <div class="nav-icon">💬</div>
                    <span>Messages</span>
                </div>
                <div class="nav-item" data-section="profile">
                    <div class="nav-icon">👤</div>
                    <span>Profile</span>
                </div>
                <div class="nav-item" data-section="communities">
                    <div class="nav-icon">🎯</div>
                    <span>Communities</span>
                </div>
                <div class="nav-item" data-section="analytics">
                    <div class="nav-icon">📊</div>
                    <span>Analytics</span>
                </div>
            </aside>

            <!-- Feed Section -->
            <main class="feed content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <div class="story-prompt">
                        <h4>✨ Share Your Story</h4>
                        <p>What positive experience would you like to share with the community today?</p>
                        <button class="create-post-btn" id="createPostBtn">Create Post</button>
                    </div>
                </div>
                <div class="feed-content">
                    <div id="postsContainer">
                        <!-- Posts will be loaded here -->
                    </div>
                    <div class="load-more">Load More Posts</div>
                </div>
            </main>

            <!-- Explore Section -->
            <section class="feed content-section" id="explore-section" style="display: none;">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                </div>
                <div class="feed-content">
                    <div class="explore-content">
                        <h3>🔍 Discover New Content</h3>
                        <p>Explore trending posts and discover new voices in the community.</p>
                        <div class="explore-categories">
                            <div class="category-tag">#Trending</div>
                            <div class="category-tag">#BlackExcellence</div>
                            <div class="category-tag">#CommunityLove</div>
                            <div class="category-tag">#Inspiration</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="feed content-section" id="messages-section" style="display: none;">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                </div>
                <div class="feed-content">
                    <div class="messages-content">
                        <h3>💬 Your Messages</h3>
                        <p>Connect and communicate with your community.</p>
                        <div class="messages-placeholder">
                            <p>No messages yet. Start a conversation!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="feed content-section" id="profile-section" style="display: none;">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                </div>
                <div class="feed-content">
                    <div class="profile-content">
                        <div class="profile-header">
                            <div class="profile-avatar">👤</div>
                            <div class="profile-info">
                                <h3 id="profileUsername">Loading...</h3>
                                <p id="profileEmail">Loading...</p>
                            </div>
                        </div>
                        <div class="profile-stats">
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Followers</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Following</span>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <button class="nav-btn" id="signOutBtn">Sign Out</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Communities Section -->
            <section class="feed content-section" id="communities-section" style="display: none;">
                <div class="feed-header">
                    <h2 class="feed-title">Communities</h2>
                </div>
                <div class="feed-content">
                    <div class="communities-content">
                        <h3>🎯 Join Communities</h3>
                        <p>Connect with like-minded people and join communities that inspire you.</p>
                        <div class="communities-grid">
                            <div class="community-card">
                                <h4>Black Excellence</h4>
                                <p>Celebrating achievements and success stories</p>
                                <button class="join-btn">Join</button>
                            </div>
                            <div class="community-card">
                                <h4>Entrepreneurs</h4>
                                <p>Business owners and aspiring entrepreneurs</p>
                                <button class="join-btn">Join</button>
                            </div>
                            <div class="community-card">
                                <h4>Creative Arts</h4>
                                <p>Artists, musicians, and creative minds</p>
                                <button class="join-btn">Join</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section class="feed content-section" id="analytics-section" style="display: none;">
                <div class="feed-header">
                    <h2 class="feed-title">Analytics</h2>
                </div>
                <div class="feed-content">
                    <div class="analytics-content">
                        <h3>📊 Your Analytics</h3>
                        <p>Track your engagement and community impact.</p>
                        <div class="analytics-grid">
                            <div class="analytics-card">
                                <h4>Post Views</h4>
                                <div class="analytics-number">1,234</div>
                                <p>Total views this month</p>
                            </div>
                            <div class="analytics-card">
                                <h4>Engagement</h4>
                                <div class="analytics-number">89%</div>
                                <p>Engagement rate</p>
                            </div>
                            <div class="analytics-card">
                                <h4>Followers</h4>
                                <div class="analytics-number">567</div>
                                <p>New followers this month</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <aside class="trending">
                <h3>Trending Topics</h3>
                <div class="trending-item">
                    <div class="trending-hashtag">#BlackExcellence</div>
                    <div class="trending-count">2.1M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#CommunityLove</div>
                    <div class="trending-count">980K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Inspiration</div>
                    <div class="trending-count">1.5M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#BlackJoy</div>
                    <div class="trending-count">750K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Success</div>
                    <div class="trending-count">1.2M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Empowerment</div>
                    <div class="trending-count">890K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Culture</div>
                    <div class="trending-count">650K posts</div>
                </div>
            </aside>
        </div>
    </div>

    <script>
        // Enhanced navigation system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing navigation system...');

            // Navigation functionality
            function switchToSection(sectionName) {
                console.log('🔄 Switching to section:', sectionName);

                // Hide all sections
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Show target section
                const targetSection = document.getElementById(`${sectionName}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                    console.log('✅ Switched to section:', sectionName);
                } else {
                    console.error('❌ Section not found:', sectionName);
                    // Fallback to feed
                    document.getElementById('feed-section').classList.add('active');
                }

                // Update navigation active states
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === sectionName) {
                        item.classList.add('active');
                    }
                });

                // Update URL hash
                window.history.pushState(null, null, `#${sectionName}`);
            }

            // Navigation item interactions
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const sectionName = this.dataset.section;
                    if (sectionName) {
                        switchToSection(sectionName);
                    }
                });
            });

            // Handle browser back/forward
            window.addEventListener('popstate', function() {
                const hash = window.location.hash.substring(1);
                const section = hash || 'feed';
                switchToSection(section);
            });

            // Initialize with correct section
            const initialHash = window.location.hash.substring(1);
            const initialSection = initialHash || 'feed';
            switchToSection(initialSection);

            // Search functionality
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });
            searchInput.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });

            // Trending item interactions
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', function() {
                    const hashtag = this.querySelector('.trending-hashtag').textContent;
                    searchInput.value = hashtag;
                    searchInput.focus();
                });
            });

            // Create post button animation
            const createPostBtn = document.querySelector('.create-post-btn');
            if (createPostBtn) {
                createPostBtn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);

                    // Open create post modal if Posts module is available
                    if (window.Posts && typeof window.Posts.openCreatePostModal === 'function') {
                        window.Posts.openCreatePostModal();
                    } else {
                        console.log('Create post functionality will be implemented');
                    }
                });
            }

            // Profile image interaction
            const profileImg = document.querySelector('.profile-img');
            profileImg.addEventListener('click', function() {
                this.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                // Switch to profile section
                switchToSection('profile');
            });

            // Sign out button functionality
            const signOutBtn = document.getElementById('signOutBtn');
            if (signOutBtn) {
                signOutBtn.addEventListener('click', async function() {
                    const confirmSignOut = confirm('Are you sure you want to sign out?');
                    if (!confirmSignOut) return;

                    try {
                        // Show loading state
                        this.textContent = 'Signing out...';
                        this.disabled = true;

                        // Clear local storage
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('authToken');

                        // Redirect to landing page
                        window.location.href = '/landing.html';
                    } catch (error) {
                        console.error('Sign out error:', error);
                        alert('Error signing out. Please try again.');
                        this.textContent = 'Sign Out';
                        this.disabled = false;
                    }
                });
            }

            // Load user profile data
            function loadProfileData() {
                try {
                    const currentUser = localStorage.getItem('currentUser');
                    if (currentUser) {
                        const userData = JSON.parse(currentUser);

                        const profileUsername = document.getElementById('profileUsername');
                        const profileEmail = document.getElementById('profileEmail');

                        if (profileUsername) {
                            profileUsername.textContent = userData.username || userData.displayName || 'User';
                        }

                        if (profileEmail) {
                            profileEmail.textContent = userData.email || 'No email provided';
                        }
                    }
                } catch (error) {
                    console.error('Error loading profile data:', error);
                }
            }

            // Load profile data on initialization
            loadProfileData();

            console.log('✅ Navigation system initialized successfully');
        });
    </script>









    <!-- Include essential JavaScript files for functionality -->
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>
</body>
</html>
